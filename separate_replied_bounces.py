import os
import pandas as pd
import re

from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.table import Table

console = Console()

def clean_filename(name):
    """
    Clean a string to be used as a filename by removing invalid characters
    and replacing spaces around hyphens.
    """
    if pd.isna(name) or name == '':
        return 'Unknown'
    
    # Convert to string and strip whitespace
    name = str(name).strip()
    
    # Replace spaces around hyphens (e.g., 'M-Nano - 2025' becomes 'M-Nano-2025')
    name = re.sub(r'\s*-\s*', '-', name)
    
    # Remove or replace invalid filename characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        name = name.replace(char, '_')
    
    # Remove extra spaces and replace with underscores
    name = re.sub(r'\s+', '_', name)
    
    return name

def separate_excel_by_column(file_path, column_name='Conference Short Name'):
    """
    Separate Excel file data based on a specified column and save to separated folder.
    
    Args:
        file_path: Path to the Excel file
        column_name: Name of the column to separate by
    """
    console.print(f"[bold blue]Processing Excel file:[/bold blue] {file_path}")
    
    # Check if file exists
    if not os.path.exists(file_path):
        console.print(f"[bold red]Error: File not found:[/bold red] {file_path}")
        return
    
    try:
        # Read the Excel file
        console.print("[yellow]Reading Excel file...[/yellow]")
        df = pd.read_excel(file_path)
        
        console.print(f"[green]✓ Successfully loaded {len(df)} rows[/green]")
        
        # Display column information
        console.print(f"[cyan]Available columns:[/cyan] {', '.join(df.columns.tolist())}")
        
        # Check if the specified column exists
        if column_name not in df.columns:
            console.print(f"[bold red]Error: Column '{column_name}' not found in the file.[/bold red]")
            console.print(f"[yellow]Available columns are:[/yellow] {', '.join(df.columns.tolist())}")
            return
        
        # Get the directory of the input file
        input_dir = os.path.dirname(file_path)
        
        # Create separated directory
        separated_dir = os.path.join(input_dir, "separated")
        os.makedirs(separated_dir, exist_ok=True)
        console.print(f"[green]✓ Created separated directory:[/green] {separated_dir}")
        
        # Get unique values from the specified column
        unique_values = df[column_name].dropna().unique()
        console.print(f"[cyan]Found {len(unique_values)} unique values in '{column_name}' column[/cyan]")
        
        # Create a summary table
        summary_table = Table(title="Separation Summary")
        summary_table.add_column("Conference Short Name", style="cyan")
        summary_table.add_column("Filename", style="green")
        summary_table.add_column("Rows", style="yellow", justify="right")
        
        # Process each unique value with progress bar
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=console
        ) as progress:
            
            task = progress.add_task("Separating data...", total=len(unique_values))
            
            for value in unique_values:
                # Filter data for this value
                filtered_df = df[df[column_name] == value].copy()
                
                # Clean the value for filename
                clean_value = clean_filename(value)
                
                # Create output filename
                output_filename = f"{clean_value}_replied_bounces.csv"
                output_path = os.path.join(separated_dir, output_filename)

                # Save the filtered data
                filtered_df.to_csv(output_path, index=False, encoding='utf-8-sig')
                
                # Add to summary table
                summary_table.add_row(str(value), output_filename, str(len(filtered_df)))
                
                progress.advance(task)
        
        # Display summary
        console.print("\n")
        console.print(summary_table)
        
        console.print(f"\n[bold green]✓ Separation complete![/bold green]")
        console.print(f"[green]Files saved to:[/green] {separated_dir}")
        
        # Handle rows with missing values in the column
        missing_data = df[df[column_name].isna()]
        if not missing_data.empty:
            console.print(f"\n[yellow]⚠ Found {len(missing_data)} rows with missing '{column_name}' values[/yellow]")
            
            # Save missing data to a separate file
            missing_filename = "Missing_Conference_replied_bounces.csv"
            missing_path = os.path.join(separated_dir, missing_filename)
            missing_data.to_csv(missing_path, index=False, encoding='utf-8-sig')
            console.print(f"[yellow]Saved rows with missing values to:[/yellow] {missing_filename}")
        
    except Exception as e:
        console.print(f"[bold red]Error processing file:[/bold red] {str(e)}")

def main():
    """Main function to run the separation process."""
    console.print("[bold blue]Excel File Separator by Conference Short Name[/bold blue]")
    console.print("=" * 60)
    
    # Default file path based on user's request
    default_file_path = r"H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs\replied bounces\Replied Bounces 07-07-2025.xlsx"
    
    # Ask user for file path or use default
    console.print(f"[cyan]Default file path:[/cyan] {default_file_path}")
    user_input = input("Press Enter to use default path, or enter a different path: ").strip()
    
    file_path = user_input if user_input else default_file_path
    
    # Run the separation
    separate_excel_by_column(file_path, 'Conference Short Name')

if __name__ == "__main__":
    main()
